#!/bin/bash

# Fix the final remaining issues with toString() methods and type mismatches

echo "Fixing final toString() and type issues..."

# List of files that need fixing
files=(
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestBorrowerWorkflowData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestCreditPullData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestCreditScoreData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestDocumentData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestDocumentWorkflowData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestFileData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestIncomeData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestLiabilityData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestLoanAmountData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestOwnedPropertyData.java"
  "wealthfront-platform/src/test/java/com/wealthfront/branchy/TestPropertyData.java"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "Processing $file"
    
    # Fix getId() methods that incorrectly return TestMortgageDataId.of(id)
    # Pattern: return id != null ? TestMortgageDataId.of(id) : null;
    # Should be: return id;
    sed -i '' 's/return id != null ? TestMortgageDataId\.of(id) : null;/return id;/g' "$file"
    
    # Fix getInternalId() methods that call getId() incorrectly
    # Pattern: return getId();
    # Should be: return id != null ? TestMortgageDataId.of(id) : null;
    sed -i '' 's/return getId();/return id != null ? TestMortgageDataId.of(id) : null;/g' "$file"
    
    # Fix setInternalId() methods that assign TestMortgageDataId to String
    # Pattern: this.id = id != null ? id : null;
    # Should be: this.id = id != null ? id.getId() : null;
    sed -i '' 's/this\.id = id != null ? id : null;/this.id = id != null ? id.getId() : null;/g' "$file"
    
  else
    echo "File not found: $file"
  fi
done

echo "Done fixing final issues"
