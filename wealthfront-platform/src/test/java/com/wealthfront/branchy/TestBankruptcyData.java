package com.wealthfront.branchy;

import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestBankruptcyData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestBankruptcyChapterType bankruptcyChapterType;

  TestBankruptcyData() {/* JSON */}

  TestBankruptcyData(TestEntityAction action, TestBankruptcyChapterType chapterType) {
    this.action = action;
    this.bankruptcyChapterType = chapterType;
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Set.of() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.BANKRUPTCY;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

  public Option<TestBankruptcyChapterType> getBankruptcyChapterType() {
    return Option.of(bankruptcyChapterType);
  }

}
