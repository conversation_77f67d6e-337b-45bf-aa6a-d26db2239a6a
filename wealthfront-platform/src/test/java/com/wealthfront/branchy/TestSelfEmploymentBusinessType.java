package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestSelfEmploymentBusinessType {
  SOLE_PROPRIETORSHIP {
    @Override
    public <T> T visit(SelfEmploymentRoleVisitor<T> visitor) {
      return visitor.visitSoleProprietor();
    }
  },
  SINGLE_MEMBER {
    @Override
    public <T> T visit(SelfEmploymentRoleVisitor<T> visitor) {
      return visitor.visitOwner();
    }
  },
  PARTNERSHIP {
    @Override
    public <T> T visit(SelfEmploymentRoleVisitor<T> visitor) {
      return visitor.visitPartner();
    }
  },
  PARTIAL_OWNER_CORPORATION {
    @Override
    public <T> T visit(SelfEmploymentRoleVisitor<T> visitor) {
      return visitor.visitPartialOwner();
    }
  };

  public abstract <T> T visit(SelfEmploymentRoleVisitor<T> visitor);

  public interface SelfEmploymentRoleVisitor<T> {

    T visitSoleProprietor();

    T visitOwner();

    T visitPartner();

    T visitPartialOwner();

  }
}
