package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestLiabilityData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String creditorName;

  @Value(optional = true)
  private String accountNumber;

  @Value(optional = true)
  private TestLiabilityType liabilityType;

  @Value(optional = true)
  private TestMortgageLoanType mortgageLoanType;

  @Value(optional = true)
  private Boolean isPrimaryMortgage;

  @Value(optional = true)
  private Money remainingBalance;

  @Value(optional = true)
  private Money monthlyPayment;

  @Value(optional = true)
  private Money helocCreditLimit;

  TestLiabilityData() { /* JSON */ }

  TestLiabilityData(
      String id, TestEntityAction action, String creditorName, String accountNumber, TestLiabilityType liabilityType,
      TestMortgageLoanType mortgageLoanType,
      Boolean isPrimaryMortgage, Money remainingBalance, Money monthlyPayment, Money helocCreditLimit) {
    this.id = id;
    this.action = action;
    this.creditorName = creditorName;
    this.accountNumber = accountNumber;
    this.liabilityType = liabilityType;
    this.mortgageLoanType = mortgageLoanType;
    this.isPrimaryMortgage = isPrimaryMortgage;
    this.remainingBalance = remainingBalance;
    this.monthlyPayment = monthlyPayment;
    this.helocCreditLimit = helocCreditLimit;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.OWNED_PROPERTY_LIABILITY;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseLiabilityData(this);
  }

  public String getId() {
    return id;
  }

  public Option<String> getCreditorName() {
    return Option.of(creditorName);
  }

  public Option<String> getAccountNumber() {
    return Option.of(accountNumber);
  }

  public Option<TestLiabilityType> getLiabilityType() {
    return Option.of(liabilityType);
  }

  public Option<TestMortgageLoanType> getMortgageLoanType() {
    return Option.of(mortgageLoanType);
  }

  public Option<Boolean> getPrimaryMortgage() {
    return Option.of(isPrimaryMortgage);
  }

  public Option<Money> getRemainingBalance() {
    return Option.of(remainingBalance);
  }

  public Option<Money> getMonthlyPayment() {
    return Option.of(monthlyPayment);
  }

  public Option<Money> getHelocCreditLimit() {
    return Option.of(helocCreditLimit);
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}
