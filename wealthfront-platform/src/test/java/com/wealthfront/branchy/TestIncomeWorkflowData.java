package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestIncomeWorkflowData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private Boolean hasCapitalGains;

  @Value(optional = true)
  private Boolean hasDividendsAndInterest;

  @Value(optional = true)
  private Boolean hasSocialSecurity;

  @Value(optional = true)
  private Boolean hasPension;

  @Value(optional = true)
  private Boolean hasRetirementAccountDistributions;

  @Value(optional = true)
  private Boolean hasDisability;

  @Value(optional = true)
  private Boolean hasAlimony;

  @Value(optional = true)
  private Boolean hasSeparateMaintenance;

  @Value(optional = true)
  private Boolean hasChildSupport;

  @Value(optional = true)
  private Boolean hasOtherIncome;

  @Value(optional = true)
  private Boolean hasReviewedEmployment;

  TestIncomeWorkflowData() { /* JSON */ }

  TestIncomeWorkflowData(
      TestEntityAction action,
      Boolean hasCapitalGains, Boolean hasDividendsAndInterest, Boolean hasSocialSecurity,
      Boolean hasPension, Boolean hasRetirementAccountDistributions, Boolean hasDisability,
      Boolean hasAlimony, Boolean hasSeparateMaintenance, Boolean hasChildSupport,
      Boolean hasOtherIncome, Boolean hasReviewedEmployment) {
    this.action = action;
    this.hasCapitalGains = hasCapitalGains;
    this.hasDividendsAndInterest = hasDividendsAndInterest;
    this.hasSocialSecurity = hasSocialSecurity;
    this.hasPension = hasPension;
    this.hasRetirementAccountDistributions = hasRetirementAccountDistributions;
    this.hasDisability = hasDisability;
    this.hasAlimony = hasAlimony;
    this.hasSeparateMaintenance = hasSeparateMaintenance;
    this.hasChildSupport = hasChildSupport;
    this.hasOtherIncome = hasOtherIncome;
    this.hasReviewedEmployment = hasReviewedEmployment;
  }

  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.INCOME_WORKFLOW;
  }


  @Override
  public TestEntityAction getAction() {
    return action;
  }

  public Option<Boolean> getHasCapitalGains() {
    return Option.of(hasCapitalGains);
  }

  public Option<Boolean> getHasDividendsAndInterest() {
    return Option.of(hasDividendsAndInterest);
  }

  public Option<Boolean> getHasSocialSecurity() {
    return Option.of(hasSocialSecurity);
  }

  public Option<Boolean> getHasPension() {
    return Option.of(hasPension);
  }

  public Option<Boolean> getHasRetirementAccountDistributions() {
    return Option.of(hasRetirementAccountDistributions);
  }

  public Option<Boolean> getHasDisability() {
    return Option.of(hasDisability);
  }

  public Option<Boolean> getHasAlimony() {
    return Option.of(hasAlimony);
  }

  public Option<Boolean> getHasSeparateMaintenance() {
    return Option.of(hasSeparateMaintenance);
  }

  public Option<Boolean> getHasChildSupport() {
    return Option.of(hasChildSupport);
  }

  public Option<Boolean> getHasOtherIncome() {
    return Option.of(hasOtherIncome);
  }

  public Option<Boolean> getHasReviewedEmployment() {
    return Option.of(hasReviewedEmployment);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.NO_OP;
  }

}
