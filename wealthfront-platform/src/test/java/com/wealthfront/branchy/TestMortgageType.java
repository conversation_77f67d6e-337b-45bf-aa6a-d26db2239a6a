package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestMortgageType {
  PURCHASE {
    @Override
    public <T> T visit(MortgageTypeVisitor<T> visitor) {
      return visitor.visitPurchase();
    }
  },
  REFINANCE {
    @Override
    public <T> T visit(MortgageTypeVisitor<T> visitor) {
      return visitor.visitRefinance();
    }
  },
  HELOC {
    @Override
    public <T> T visit(MortgageTypeVisitor<T> visitor) {
      return visitor.visitHeloc();
    }
  };

  public abstract <T> T visit(MortgageTypeVisitor<T> visitor);

  public interface MortgageTypeVisitor<T> {
    T visitPurchase();

    T visitRefinance();

    T visitHeloc();
  }
}
