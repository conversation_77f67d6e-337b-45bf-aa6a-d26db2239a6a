package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestPropertyType {
  SINGLE_FAMILY {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitSingleFamily();
    }
  },
  CONDO {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitCondo();
    }
  },
  TOWNHOUSE {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitTownhouse();
    }
  },
  MULTI_FAMILY {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitMultiFamily();
    }
  },
  MANUFACTURED_HOME {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitManufacturedHome();
    }
  },
  OTHER {
    @Override
    public <T> T visit(PropertyTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  };

  public abstract <T> T visit(PropertyTypeVisitor<T> visitor);

  public interface PropertyTypeVisitor<T> {
    T visitSingleFamily();

    T visitCondo();

    T visitTownhouse();

    T visitMultiFamily();

    T visitManufacturedHome();

    T visitOther();
  }
}
