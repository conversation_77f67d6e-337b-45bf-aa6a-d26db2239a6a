package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import org.joda.time.DateTime;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.navigation.VoyagerStepId;

@Entity
class TestDocumentWorkflowData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value
  private String id;

  @Value(optional = true)
  private DateTime createdAt;

  @Value(optional = true)
  private VoyagerStepId voyagerStepId;

  @Value(optional = true)
  private Visibility visibility;

  TestDocumentWorkflowData() { /* JSON */ }

  TestDocumentWorkflowData(
      TestEntityAction action, String id, DateTime createdAt,
      VoyagerStepId voyagerStepId, Visibility visibility) {
    this.action = action;
    this.id = id;
    this.createdAt = createdAt;
    this.voyagerStepId = voyagerStepId;
    this.visibility = visibility;
  }

  public Option<DateTime> getCreatedAt() {
    return Option.of(createdAt);
  }

  public Option<VoyagerStepId> getVoyagerStepId() {
    return Option.of(voyagerStepId);
  }

  public Option<Visibility> getVisibility() {
    return Option.of(visibility);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.DOCUMENT_WORKFLOW;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.NO_OP;
  }

  @ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
  public enum Visibility {
    BORROWER,
    MORTGAGE_OPS;
  }

}

