package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestAssetCategory {
  CASH_ASSET("Cash assets") {
    @Override
    public <T> T visit(AssetCategoryVisitor<T> visitor) {
      return visitor.caseCashAsset();
    }
  },
  INVESTMENT_ACCOUNT("Investment accounts") {
    @Override
    public <T> T visit(AssetCategoryVisitor<T> visitor) {
      return visitor.caseInvestmentAccount();
    }
  },
  RETIREMENT_ACCOUNT("Retirement accounts") {
    @Override
    public <T> T visit(AssetCategoryVisitor<T> visitor) {
      return visitor.caseRetirementAccount();
    }
  },
  GIFT("Gifts from others") {
    @Override
    public <T> T visit(AssetCategoryVisitor<T> visitor) {
      return visitor.caseGift();
    }
  },
  OTHER("Other assets") {
    @Override
    public <T> T visit(AssetCategoryVisitor<T> visitor) {
      return visitor.caseOther();
    }
  };

  private final String name;

  TestAssetCategory(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }

  public abstract <T> T visit(AssetCategoryVisitor<T> visitor);

  public interface AssetCategoryVisitor<T> {
    T caseCashAsset();

    T caseInvestmentAccount();

    T caseRetirementAccount();

    T caseGift();

    T caseOther();
  }
}
