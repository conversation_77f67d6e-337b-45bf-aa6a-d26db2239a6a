package com.wealthfront.branchy;

import static com.wealthfront.branchy.TestEntityAction.UPDATE;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
public class TestApplicationData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestMortgageType mortgageType;

  @Value(optional = true)
  private TestJourneyStage journeyStage;

  @Value(optional = true)
  private List<TestBorrowerData> borrowers;

  @Value(optional = true)
  private TestPropertyData subjectProperty;

  @Value(optional = true)
  private List<TestContactData> contacts;

  @Value(optional = true)
  private List<TestIncomeData> incomes;

  @Value(optional = true)
  private List<TestDocumentData> documents;

  @Value(optional = true)
  private List<TestAssetData> assets;

  @Value(optional = true)
  private TestLoanAmountData loanAmount;

  @Value(optional = true)
  private List<TestOwnedPropertyData> ownedProperties;

  @Value(optional = true)
  private List<TestLiabilityData> liabilities;

  @Value(optional = true)
  private List<TestCreditPullData> creditPulls;

  TestApplicationData() { /* JSON */ }

  TestApplicationData(
      TestMortgageType mortgageType, TestJourneyStage journeyStage,
      List<TestBorrowerData> borrowers,
      TestPropertyData subjectProperty, List<TestContactData> contacts, List<TestIncomeData> incomes, List<TestDocumentData> documents,
      List<TestAssetData> assets, TestLoanAmountData loanAmount, List<TestOwnedPropertyData> ownedProperties, List<TestLiabilityData> liabilities,
      List<TestCreditPullData> creditPulls) {
    this.mortgageType = mortgageType;
    this.journeyStage = journeyStage;
    this.borrowers = borrowers;
    this.subjectProperty = subjectProperty;
    this.contacts = contacts;
    this.incomes = incomes;
    this.documents = documents;
    this.assets = assets;
    this.loanAmount = loanAmount;
    this.ownedProperties = ownedProperties;
    this.liabilities = liabilities;
    this.creditPulls = creditPulls;
  }

  public Option<TestMortgageType> getMortgageType() {
    return Option.of(mortgageType);
  }

  public Option<TestJourneyStage> getJourneyStage() {
    return Option.of(journeyStage);
  }

  public Option<List<TestBorrowerData>> getBorrowers() {
    return Option.of(borrowers);
  }

  public Option<TestPropertyData> getSubjectProperty() {
    return Option.of(subjectProperty);
  }

  public Option<List<TestContactData>> getContacts() {
    return Option.of(contacts);
  }

  public Option<List<TestIncomeData>> getIncomes() {
    return Option.of(incomes);
  }

  public Option<List<TestDocumentData>> getDocuments() {
    return Option.of(documents);
  }

  public Option<List<TestAssetData>> getAssets() {
    return Option.of(assets);
  }

  public Option<TestLoanAmountData> getLoanAmount() {
    return Option.of(loanAmount);
  }

  public Option<List<TestOwnedPropertyData>> getOwnedProperties() {
    return Option.of(ownedProperties);
  }

  public Option<List<TestLiabilityData>> getLiabilities() {
    return Option.of(liabilities);
  }

  public Option<List<TestCreditPullData>> getCreditPulls() {
    return Option.of(creditPulls);
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

  @Override
  public String getInternalId() {
    return id.toString();
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return TestEntityAction.UPDATE;
  }

  @Override
  public void setAction(TestEntityAction action) {
    throw new RuntimeException(Strings.format("TestApplicationData must always have Entity Action %s", UPDATE));
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.LOAN;
  }

}
