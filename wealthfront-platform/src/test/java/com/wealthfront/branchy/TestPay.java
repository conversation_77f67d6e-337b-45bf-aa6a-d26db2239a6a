package com.wealthfront.branchy;

import static com.wealthfront.branchy.TestIncomePayFrequency.ANNUAL;
import static com.wealthfront.branchy.TestIncomePayFrequency.MONTHLY;

import java.util.List;
import java.util.Objects;

import org.joda.time.LocalDate;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.entities.Price;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity(
    discriminatorName = "type",
    subclasses = {
        TestPay.RestrictedStockPay.class,
        TestPay.BasicPay.class,
        TestPay.HourlyPay.class
    }
)
@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public abstract class TestPay {

  @Value(optional = true)
  private Money amount;

  @Value(optional = true)
  private TestIncomePayFrequency frequency;

  TestPay() { /* JSON */ }

  TestPay(Money amount, TestIncomePayFrequency frequency) {
    this.amount = amount;
    this.frequency = frequency;
  }

  public static BasicPay annualPay(Money amount) {
    return new BasicPay(amount, ANNUAL);
  }

  public static BasicPay monthlyPay(Money amount) {
    return new BasicPay(amount, MONTHLY);
  }

  public Option<Money> getAmount() {
    return Option.of(amount);
  }

  public Option<TestIncomePayFrequency> getFrequency() {
    return Option.of(frequency);
  }

  public static RestrictedStockPayBuilder restrictedStockPayBuilder() {
    return new RestrictedStockPayBuilder();
  }

  public static HourlyPayBuilder hourlyPayBuilder() {
    return new HourlyPayBuilder();
  }

  @Override
  public boolean equals(Object o) {
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestPay pay = (TestPay) o;
    return Objects.equals(amount, pay.amount) && frequency == pay.frequency;
  }

  @Override
  public int hashCode() {
    return Objects.hash(amount, frequency);
  }

  public abstract <T> T visit(PayVisitor<T> visitor);

  @Entity(discriminator = "basic")
  public static class BasicPay extends TestPay {

    public BasicPay() { /* JSON */ }

    public BasicPay(Money amount, TestIncomePayFrequency frequency) {
      super(amount, frequency);
    }

    @Override
    public <T> T visit(PayVisitor<T> visitor) {
      return visitor.visitBasicPay(this);
    }

  }

  @Entity(discriminator = "hourly")
  public static class HourlyPay extends TestPay {

    @Value(optional = true)
    private Integer avgHoursPerWeek;

    public HourlyPay() { /* JSON */ }

    public HourlyPay(Integer avgHoursPerWeek, Money amount) {
      super(amount, TestIncomePayFrequency.HOURLY);
      this.avgHoursPerWeek = avgHoursPerWeek;
    }

    public Option<Integer> getAvgHoursPerWeek() {
      return Option.of(avgHoursPerWeek);
    }

    @Override
    public <T> T visit(PayVisitor<T> visitor) {
      return visitor.visitHourlyPay(this);
    }

    @Override
    public boolean equals(Object o) {
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      if (!super.equals(o)) {
        return false;
      }
      HourlyPay hourlyPay = (HourlyPay) o;
      return Objects.equals(avgHoursPerWeek, hourlyPay.avgHoursPerWeek);
    }

    @Override
    public int hashCode() {
      return Objects.hash(super.hashCode(), avgHoursPerWeek);
    }

  }

  public static class HourlyPayBuilder {
    private Integer avgHoursPerWeek;
    private Money amount;

    public HourlyPayBuilder withAvgHoursPerWeek(Integer avgHoursPerWeek) {
      this.avgHoursPerWeek = avgHoursPerWeek;
      return this;
    }

    public HourlyPayBuilder withAmount(Money amount) {
      this.amount = amount;
      return this;
    }

    public HourlyPay build() {
      return new HourlyPay(avgHoursPerWeek, amount);
    }
  }

  @Entity(discriminator = "restricted-stock")
  public static class RestrictedStockPay extends TestPay {

    @Value(optional = true)
    private String stockTicker;

    @Value(optional = true)
    private Integer monthsReceived;

    @Value(optional = true)
    private Long sharesReceived;

    @Value(optional = true)
    private Long expectedShares;

    @Value(optional = true)
    private Boolean continuance;

    @Value(optional = true)
    private List<RSUQuote> priceData;

    public RestrictedStockPay() { /* JSON */ }

    public RestrictedStockPay(
        Money amount, TestIncomePayFrequency frequency, String stockTicker, Integer monthsReceived,
        Long sharesReceived, Long expectedShares, Boolean continuance, List<RSUQuote> priceData) {
      super(amount, frequency);
      this.stockTicker = stockTicker;
      this.monthsReceived = monthsReceived;
      this.sharesReceived = sharesReceived;
      this.expectedShares = expectedShares;
      this.continuance = continuance;
      this.priceData = priceData;
    }

    public Option<String> getStockTicker() {
      return Option.of(stockTicker);
    }

    public Option<Integer> getMonthsReceived() {
      return Option.of(monthsReceived);
    }

    public Option<Long> getSharesReceived() {
      return Option.of(sharesReceived);
    }

    public Option<Long> getExpectedShares() {
      return Option.of(expectedShares);
    }

    public Option<Boolean> getContinuance() {
      return Option.of(continuance);
    }

    public Option<List<RSUQuote>> getPriceData() {
      return Option.of(priceData);
    }

    @Override
    public <T> T visit(PayVisitor<T> visitor) {
      return visitor.visitRestrictedStockPay(this);
    }

    @Override
    public boolean equals(Object o) {
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      if (!super.equals(o)) {
        return false;
      }
      RestrictedStockPay that = (RestrictedStockPay) o;
      return Objects.equals(stockTicker, that.stockTicker) &&
          Objects.equals(monthsReceived, that.monthsReceived) &&
          Objects.equals(sharesReceived, that.sharesReceived) &&
          Objects.equals(expectedShares, that.expectedShares) &&
          Objects.equals(continuance, that.continuance) &&
          Objects.equals(priceData, that.priceData);
    }

    @Override
    public int hashCode() {
      return Objects.hash(super.hashCode(), stockTicker, monthsReceived, sharesReceived, expectedShares, continuance,
          priceData);
    }

    @Entity
    public record RSUQuote(LocalDate date, Price price) {}

  }

  public static class RestrictedStockPayBuilder {
    private Money amount;
    private TestIncomePayFrequency frequency;
    private String stockTicker;
    private Integer monthsReceived;
    private Long sharesReceived;
    private Long expectedShares;
    private Boolean continuance;
    private List<RestrictedStockPay.RSUQuote> priceData;

    public RestrictedStockPayBuilder withAmount(Money amount) {
      this.amount = amount;
      return this;
    }

    public RestrictedStockPayBuilder withFrequency(TestIncomePayFrequency frequency) {
      this.frequency = frequency;
      return this;
    }

    public RestrictedStockPayBuilder withStockTicker(String stockTicker) {
      this.stockTicker = stockTicker;
      return this;
    }

    public RestrictedStockPayBuilder withMonthsReceived(Integer monthsReceived) {
      this.monthsReceived = monthsReceived;
      return this;
    }

    public RestrictedStockPayBuilder withSharesReceived(Long sharesReceived) {
      this.sharesReceived = sharesReceived;
      return this;
    }

    public RestrictedStockPayBuilder withExpectedShares(Long expectedShares) {
      this.expectedShares = expectedShares;
      return this;
    }

    public RestrictedStockPayBuilder withContinuance(Boolean continuance) {
      this.continuance = continuance;
      return this;
    }

    public RestrictedStockPayBuilder withPriceData(List<RestrictedStockPay.RSUQuote> priceData) {
      this.priceData = priceData;
      return this;
    }

    public RestrictedStockPay build() {
      return new RestrictedStockPay(amount, frequency, stockTicker, monthsReceived,
          sharesReceived, expectedShares, continuance, priceData);
    }
  }

  public interface PayVisitor<T> {

    T visitRestrictedStockPay(RestrictedStockPay restrictedStockPay);

    T visitBasicPay(BasicPay basicPay);

    T visitHourlyPay(HourlyPay hourlyPay);

  }

}
