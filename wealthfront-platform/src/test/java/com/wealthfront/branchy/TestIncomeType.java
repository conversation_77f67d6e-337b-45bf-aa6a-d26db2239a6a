package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestIncomeType {
  EMPLOYMENT {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitEmployment();
    }
  },
  MILITARY_EMPLOYMENT {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitMilitaryEmployment();
    }
  },
  INDEPENDENT_CONTRACTOR {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitIndependentContractor();
    }
  },
  SELF_EMPLOYED {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitSelfEmployed();
    }
  },
  CAPITAL_GAINS {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitCapitalGains();
    }
  },
  DIVIDENDS_AND_INTEREST {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitDividendsAndInterest();
    }
  },
  SOCIAL_SECURITY {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitSocialSecurity();
    }
  },
  PENSION {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitPension();
    }
  },
  RETIREMENT_ACCOUNT_DISTRIBUTION {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitRetirementAccountDistribution();
    }
  },
  DISABILITY {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitDisability();
    }
  },
  AUTOMOBILE_ALLOWANCE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  HOUSING_ALLOWANCE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  HOUSING_CHOICE_VOUCHER_PROGRAM {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  MORTGAGE_CREDIT_CERTIFICATE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  MORTGAGE_DIFFERENTIAL {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  NOTES_RECEIVABLE_INSTALLMENT {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  DEFERRED_COMPENSATION {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  UNEMPLOYMENT {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  PUBLIC_ASSISTANCE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  VA_BENEFITS {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  FOSTER_CARE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  ROYALTIES {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  TRUST {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  TEMPORARY_LEAVE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  ALIMONY {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitAlimony();
    }
  },
  CHILD_SUPPORT {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitChildSupport();
    }
  },
  SEPARATE_MAINTENANCE {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitSeparateMaintenance();
    }
  },
  RENTAL_INCOME {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  },
  OTHER {
    @Override
    public <T> T visit(IncomeTypeVisitor<T> visitor) {
      return visitor.visitOther();
    }
  };

  public abstract <T> T visit(IncomeTypeVisitor<T> visitor);

  public interface IncomeTypeVisitor<T> {

    T visitEmployment();

    T visitMilitaryEmployment();

    T visitIndependentContractor();

    T visitSelfEmployed();

    T visitChildSupport();

    T visitAlimony();

    T visitCapitalGains();

    T visitDividendsAndInterest();

    T visitSocialSecurity();

    T visitPension();

    T visitDisability();

    T visitRetirementAccountDistribution();

    T visitSeparateMaintenance();

    T visitOther();

  }

  public static class ThrowingIncomeTypeVisitor<T> implements IncomeTypeVisitor<T> {

    @Override
    public T visitEmployment() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitMilitaryEmployment() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitIndependentContractor() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitSelfEmployed() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitChildSupport() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitAlimony() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitCapitalGains() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitDividendsAndInterest() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitSocialSecurity() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitPension() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitRetirementAccountDistribution() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitSeparateMaintenance() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitDisability() {
      throw new UnsupportedOperationException();
    }

    @Override
    public T visitOther() {
      throw new UnsupportedOperationException();
    }
  }

}