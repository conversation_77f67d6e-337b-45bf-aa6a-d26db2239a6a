package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestPropertyData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional=true)
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional=true)
  private TestAddressData address;

  @Value(optional=true)
  private TestPropertyType propertyType;

  @Value(optional=true)
  private TestPropertyCounty propertyCounty;

  @Value(optional=true)
  private TestPropertyUsageType currentUsage;

  @Value(optional=true)
  private TestPropertyUsageType futureUsage;

  @Value(optional=true)
  private Money estimatedValue;

  @Value(optional=true)
  private Boolean isUsedForBusiness;

  @Value(optional=true)
  private Boolean hasLienDeclaration;

  TestPropertyData() { /* JSON */ }

  TestPropertyData(String id, TestEntityAction action, TestAddressData address, TestPropertyType propertyType, TestPropertyCounty propertyCounty, TestPropertyUsageType currentUsage,
      TestPropertyUsageType futureUsage, Money estimatedValue, Boolean isUsedForBusiness, Boolean hasLienDeclaration) {
    this.id = id;
    this.action = action;
    this.address = address;
    this.propertyType = propertyType;
    this.propertyCounty = propertyCounty;
    this.currentUsage = currentUsage;
    this.futureUsage = futureUsage;
    this.estimatedValue = estimatedValue;
    this.isUsedForBusiness = isUsedForBusiness;
    this.hasLienDeclaration = hasLienDeclaration;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.PROPERTY;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.casePropertyData(this);
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  public String getId() {
    return id;
  }

  public Option<TestAddressData> getAddress() {
    return Option.of(address);
  }

  public Option<TestPropertyType> getPropertyType() {
    return Option.of(propertyType);
  }

  public Option<TestPropertyCounty> getPropertyCounty() {
    return Option.of(propertyCounty);
  }

  public Option<TestPropertyUsageType> getCurrentUsage() {
    return Option.of(currentUsage);
  }

  public Option<TestPropertyUsageType> getFutureUsage() {
    return Option.of(futureUsage);
  }

  public Option<Money> getEstimatedValue() {
    return Option.of(estimatedValue);
  }

  public Option<Boolean> getUsedForBusiness() {
    return Option.of(isUsedForBusiness);
  }

  public Option<Boolean> getHasLienDeclaration() {
    return Option.of(hasLienDeclaration);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}
