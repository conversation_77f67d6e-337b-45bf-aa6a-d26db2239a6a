package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestEmploymentWorkflowData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value (optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private Boolean isW2Employment;

  @Value(optional = true)
  private Boolean meetsThresholdPercentEmployerOwnership;

  @Value(optional = true)
  private Boolean hasSalaryComp;

  @Value(optional = true)
  private Boolean hasHourlyComp;

  @Value(optional = true)
  private Boolean hasOvertimeComp;

  @Value(optional = true)
  private Boolean hasBonusComp;

  @Value(optional = true)
  private Boolean hasEquityComp;

  @Value(optional = true)
  private Boolean hasCommissionComp;

  @Value(optional = true)
  private Boolean hasOtherEmploymentComp;

  @Value(optional = true)
  private Boolean meetsEquityCompCriteria;

  @Value(optional = true)
  private Boolean isEmployerUSBased;

  TestEmploymentWorkflowData() { /* JSON */ }

  TestEmploymentWorkflowData(
      TestEntityAction action, Boolean isW2Employment, Boolean meetsThresholdPercentEmployerOwnership,
      Boolean hasSalaryComp, Boolean hasHourlyComp, Boolean hasOvertimeComp, Boolean hasBonusComp,
      Boolean hasEquityComp,
      Boolean hasCommissionComp, Boolean hasOtherEmploymentComp, Boolean meetsEquityCompCriteria,
      Boolean isEmployerUSBased) {
    this.action = action;
    this.isW2Employment = isW2Employment;
    this.meetsThresholdPercentEmployerOwnership = meetsThresholdPercentEmployerOwnership;
    this.hasSalaryComp = hasSalaryComp;
    this.hasHourlyComp = hasHourlyComp;
    this.hasOvertimeComp = hasOvertimeComp;
    this.hasBonusComp = hasBonusComp;
    this.hasEquityComp = hasEquityComp;
    this.hasCommissionComp = hasCommissionComp;
    this.hasOtherEmploymentComp = hasOtherEmploymentComp;
    this.meetsEquityCompCriteria = meetsEquityCompCriteria;
    this.isEmployerUSBased = isEmployerUSBased;
  }

  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.EMPLOYMENT_WORKFLOW;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  public Option<Boolean> getW2Employment() {
    return Option.of(isW2Employment);
  }

  public Option<Boolean> getMeetsThresholdPercentEmployerOwnership() {
    return Option.of(meetsThresholdPercentEmployerOwnership);
  }

  public Option<Boolean> getHasSalaryComp() {
    return Option.of(hasSalaryComp);
  }

  public Option<Boolean> getHasHourlyComp() {
    return Option.of(hasHourlyComp);
  }

  public Option<Boolean> getHasOvertimeComp() {
    return Option.of(hasOvertimeComp);
  }

  public Option<Boolean> getHasBonusComp() {
    return Option.of(hasBonusComp);
  }

  public Option<Boolean> getHasEquityComp() {
    return Option.of(hasEquityComp);
  }

  public Option<Boolean> getHasCommissionComp() {
    return Option.of(hasCommissionComp);
  }

  public Option<Boolean> getHasOtherEmploymentComp() {
    return Option.of(hasOtherEmploymentComp);
  }

  public Option<Boolean> getMeetsEquityCompCriteria() {
    return Option.of(meetsEquityCompCriteria);
  }

  public Option<Boolean> getEmployerUSBased() {
    return Option.of(isEmployerUSBased);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.NO_OP;
  }

}
