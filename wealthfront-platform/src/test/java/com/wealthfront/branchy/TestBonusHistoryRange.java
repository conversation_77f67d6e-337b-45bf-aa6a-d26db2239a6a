package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestBonusHistoryRange {
  LESS_THAN_ONE_YEAR {
    @Override
    public <T> T visit(HistoricRangeVisitor<T> visitor) {
      return visitor.visitLessThanOneYear();
    }
  },
  ONE_TO_TWO_YEARS {
    @Override
    public <T> T visit(HistoricRangeVisitor<T> visitor) {
      return visitor.visitOneToTwoYears();
    }
  },
  OVER_TWO_YEARS {
    @Override
    public <T> T visit(HistoricRangeVisitor<T> visitor) {
      return visitor.visitOverTwoYears();
    }
  };

  public abstract <T> T visit(HistoricRangeVisitor<T> visitor);

  public interface HistoricRangeVisitor<T> {
    T visitLessThanOneYear();

    T visitOneToTwoYears();

    T visitOverTwoYears();
  }
}
