package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestPropertyUsageType {
  PRIMARY_RESIDENCE {
    @Override
    public <T> T visit(PropertyUsageTypeVisitor<T> visitor) {
      return visitor.visitPrimaryResidence();
    }
  },
  SECOND_HOME {
    @Override
    public <T> T visit(PropertyUsageTypeVisitor<T> visitor) {
      return visitor.visitSecondHome();
    }
  },
  INVESTMENT {
    @Override
    public <T> T visit(PropertyUsageTypeVisitor<T> visitor) {
      return visitor.visitInvestment();
    }
  };

  public abstract <T> T visit(PropertyUsageTypeVisitor<T> visitor);

  public interface PropertyUsageTypeVisitor<T> {
    T visitPrimaryResidence();

    T visitSecondHome();

    T visitInvestment();
  }
}
