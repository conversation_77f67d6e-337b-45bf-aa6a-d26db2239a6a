package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestLoanAmountData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private Money purchasePrice;

  @Value(optional = true)
  private Money downPaymentAmount;

  @Value(optional = true)
  private Money loanAmount;

  TestLoanAmountData() { /* JSON */ }

  TestLoanAmountData(TestEntityAction action, Money purchasePrice, Money downPaymentAmount, Money loanAmount) {
    this.action = action;
    this.purchasePrice = purchasePrice;
    this.downPaymentAmount = downPaymentAmount;
    this.loanAmount = loanAmount;
  }

  public Option<Money> getPurchasePrice() {
    return Option.of(purchasePrice);
  }

  public Option<Money> getDownPaymentAmount() {
    return Option.of(downPaymentAmount);
  }

  public Option<Money> getLoanAmount() {
    return Option.of(loanAmount);
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  public String getId() {
    return id;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.LOAN_AMOUNT;
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}
