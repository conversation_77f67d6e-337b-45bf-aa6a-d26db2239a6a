package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestCreditScoreData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private String borrowerId;

  @Value(optional = true)
  TestCreditBureau bureau;

  @Value(optional = true)
  TestCreditScoreUnavailableReason unavailableReason;

  TestCreditScoreData() {
    /* JSON */
  }

  TestCreditScoreData(
      TestEntityAction action, String id, String borrowerId, TestCreditBureau bureau,
      TestCreditScoreUnavailableReason unavailableReason) {
    this.action = action;
    this.id = id;
    this.borrowerId = borrowerId;
    this.bureau = bureau;
    this.unavailableReason = unavailableReason;
  }

  public Option<String> getBorrowerId() {
    return Option.of(borrowerId);
  }

  public Option<TestCreditBureau> getBureau() {
    return Option.of(bureau);
  }

  public Option<TestCreditScoreUnavailableReason> getUnavailableReason() {
    return Option.of(unavailableReason);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.CREDIT_SCORE;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.NO_OP;
  }


}
