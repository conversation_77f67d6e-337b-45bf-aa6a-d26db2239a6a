package com.wealthfront.branchy;

import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = {
        ExposeTo.LOCAL,
        ExposeTo.BACKEND,
        ExposeTo.API_SERVER,
        ExposeTo.FRONTEND
    }
)
@Entity(discriminatorName = "type", subclasses = {
    TestAddressData.class,
    TestApplicationData.class,
    TestAssetData.class,
    TestAssetWorkflowData.class,
    TestBankruptcyData.class,
    TestBorrowerData.class,
    TestBorrowerWorkflowData.class,
    TestContactData.class,
    TestCreditPullData.class,
    TestCreditScoreData.class,
    TestDocumentData.class,
    TestDocumentWorkflowData.class,
    TestEmploymentWorkflowData.class,
    TestFileData.class,
    TestIncomeData.class,
    TestIncomeWorkflowData.class,
    TestLiabilityData.class,
    TestLoanAmountData.class,
    TestOwnedPropertyData.class,
    TestPropertyData.class
})
public abstract class TestMortgageData {

  @Value(
      optional = true,
      nullable = true
  )
  public abstract String getInternalId();

  public abstract void setInternalId(String id);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract Set<String> getFieldsToNullSet();

  public abstract void setFieldsToNullSet(Set<String> fieldsToNullSet);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestEntityAction getAction();

  public abstract void setAction(TestEntityAction action);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestDataType getDataType();

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestVestaOperationType getVestaOperationType();

}
