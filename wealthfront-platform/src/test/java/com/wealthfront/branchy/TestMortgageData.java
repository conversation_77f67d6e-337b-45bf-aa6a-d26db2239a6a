package com.wealthfront.branchy;

import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = {
        ExposeTo.LOCAL,
        ExposeTo.BACKEND,
        ExposeTo.API_SERVER,
        ExposeTo.FRONTEND
    }
)
@Entity(discriminatorName = "type", subclasses = {
    TestMortgageData.DefaultEmptyTestMortgageData.class,
    TestAddressData.class,
    TestApplicationData.class,
    TestAssetData.class,
    TestAssetWorkflowData.class,
    TestBankruptcyData.class,
    TestBorrowerData.class,
    TestBorrowerWorkflowData.class,
    TestContactData.class,
    TestCreditPullData.class,
    TestCreditScoreData.class,
    TestDocumentData.class,
    TestDocumentWorkflowData.class,
    TestEmploymentWorkflowData.class,
    TestFileData.class,
    TestIncomeData.class,
    TestIncomeWorkflowData.class,
    TestLiabilityData.class,
    TestLoanAmountData.class,
    TestOwnedPropertyData.class,
    TestPropertyData.class
})
public abstract class TestMortgageData {

  @Value(
      optional = true,
      nullable = true
  )
  String getInternalId();

  void setInternalId(String id);

  @Value(
      optional = true,
      nullable = true
  )
  Set<String> getFieldsToNullSet();

  void setFieldsToNullSet(Set<String> fieldsToNullSet);

  @Value(
      optional = true,
      nullable = true
  )
  TestEntityAction getAction();

  void setAction(TestEntityAction action);

  @Value(
      optional = true,
      nullable = true
  )
  TestDataType getDataType();

  @Value(
      optional = true,
      nullable = true
  )
  TestVestaOperationType getVestaOperationType();

  <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor);

  interface TestMortgageDataVisitor<T> {
    T caseApplicationData(TestApplicationData applicationData);
    T caseBorrowerData(TestBorrowerData borrowerData);
    T casePropertyData(TestPropertyData propertyData);
    T caseAddressData(TestAddressData addressData);
    T caseContactData(TestContactData contactData);
    T caseIncomeData(TestIncomeData incomeData);
    T caseEmploymentWorkflowData(TestEmploymentWorkflowData employmentWorkflowData);
    T caseIncomeWorkflowData(TestIncomeWorkflowData incomeWorkflowData);
    T caseDocumentData(TestDocumentData documentData);
    T caseDocumentWorkflowData(TestDocumentWorkflowData documentWorkflowData);
    T caseFileData(TestFileData fileData);
    T caseAssetData(TestAssetData assetData);
    T caseAssetWorkflowData(TestAssetWorkflowData assetWorkflowData);
    T caseLoanAmountData(TestLoanAmountData loanAmountData);
    T caseOwnedPropertyData(TestOwnedPropertyData ownedPropertyData);
    T caseLiabilityData(TestLiabilityData liabilityData);
    T caseBorrowerWorkflowData(TestBorrowerWorkflowData borrowerWorkflowData);
    T caseBankruptcyData(TestBankruptcyData bankruptcyData);
    T caseCreditScoreData(TestCreditScoreData creditScoreData);
    T caseCreditPull(TestCreditPullData creditPullData);
  }

  class DefaultTestMortgageDataVisitor<T> implements TestMortgageDataVisitor<T> {
    private final T defaultValue;

    public DefaultTestMortgageDataVisitor(T defaultValue) {
      this.defaultValue = defaultValue;
    }

    @Override
    public T caseApplicationData(TestApplicationData applicationData) {
      return defaultValue;
    }

    @Override
    public T caseBorrowerData(TestBorrowerData borrowerData) {
      return defaultValue;
    }

    @Override
    public T casePropertyData(TestPropertyData propertyData) {
      return defaultValue;
    }

    @Override
    public T caseAddressData(TestAddressData addressData) {
      return defaultValue;
    }

    @Override
    public T caseContactData(TestContactData contactData) {
      return defaultValue;
    }

    @Override
    public T caseIncomeData(TestIncomeData incomeData) {
      return defaultValue;
    }

    @Override
    public T caseEmploymentWorkflowData(TestEmploymentWorkflowData employmentWorkflowData) {
      return defaultValue;
    }

    @Override
    public T caseIncomeWorkflowData(TestIncomeWorkflowData incomeWorkflowData) {
      return defaultValue;
    }

    @Override
    public T caseDocumentData(TestDocumentData documentData) {
      return defaultValue;
    }

    @Override
    public T caseDocumentWorkflowData(TestDocumentWorkflowData documentWorkflowData) {
      return defaultValue;
    }

    @Override
    public T caseFileData(TestFileData fileData) {
      return defaultValue;
    }

    @Override
    public T caseAssetData(TestAssetData assetData) {
      return defaultValue;
    }

    @Override
    public T caseAssetWorkflowData(TestAssetWorkflowData assetWorkflowData) {
      return defaultValue;
    }

    @Override
    public T caseLoanAmountData(TestLoanAmountData loanAmountData) {
      return defaultValue;
    }

    @Override
    public T caseOwnedPropertyData(TestOwnedPropertyData ownedPropertyData) {
      return defaultValue;
    }

    @Override
    public T caseLiabilityData(TestLiabilityData liabilityData) {
      return defaultValue;
    }

    @Override
    public T caseBorrowerWorkflowData(TestBorrowerWorkflowData borrowerWorkflowData) {
      return defaultValue;
    }

    @Override
    public T caseBankruptcyData(TestBankruptcyData bankruptcyData) {
      return defaultValue;
    }

    @Override
    public T caseCreditScoreData(TestCreditScoreData creditScoreData) {
      return defaultValue;
    }

    @Override
    public T caseCreditPull(TestCreditPullData creditPullData) {
      return defaultValue;
    }
  }

  @Entity(discriminator = "default-empty")
  class DefaultEmptyTestMortgageData implements TestMortgageData {
    private String internalId;
    private Set<String> fieldsToNullSet;
    private TestEntityAction action;
    private TestDataType dataType;
    private TestVestaOperationType vestaOperationType;

    public DefaultEmptyTestMortgageData() {
      // JSON
    }

    @Override
    public String getInternalId() {
      return internalId;
    }

    @Override
    public void setInternalId(String id) {
      this.internalId = id;
    }

    @Override
    public Set<String> getFieldsToNullSet() {
      return fieldsToNullSet;
    }

    @Override
    public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
      this.fieldsToNullSet = fieldsToNullSet;
    }

    @Override
    public TestEntityAction getAction() {
      return action;
    }

    @Override
    public void setAction(TestEntityAction action) {
      this.action = action;
    }

    @Override
    public TestDataType getDataType() {
      return dataType;
    }

    @Override
    public TestVestaOperationType getVestaOperationType() {
      return vestaOperationType;
    }

    @Override
    public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
      return null;
    }
  }
}
