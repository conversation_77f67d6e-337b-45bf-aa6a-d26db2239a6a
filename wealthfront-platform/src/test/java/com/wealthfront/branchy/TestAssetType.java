package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestAssetType {
  CHECKING_ACCOUNT("Checking") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseCheckingAccount();
    }
  },
  SAVINGS_ACCOUNT("Savings") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseSavingsAccount();
    }
  },
  TAXABLE_BROKERAGE_ACCOUNT("Brokerage - Taxable") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseTaxableBrokerageAccount();
    }
  },
  STOCK("Stock") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseStock();
    }
  },
  BOND("Bond") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseBond();
    }
  },
  CERTIFICATE_OF_DEPOSIT("Certificate of Deposit") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseCertificateOfDeposit();
    }
  },
  MONEY_MARKET_FUND("Money Market Fund") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseMoneyMarketFund();
    }
  },
  MUTUAL_FUND("Mutual Fund") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseMutualFund();
    }
  },
  STOCK_OPTIONS("Stock Options") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseStockOptions();
    }
  },
  CRYPTOCURRENCY("Cryptocurrency") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseCryptocurrency();
    }
  },
  IRA("IRA") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseIRA();
    }
  },
  FOUR_ZERO_ONE_K("401K") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseFourZeroOneK();
    }
  },
  THRIFT_SAVINGS_PLAN("Thrift Savings Plan") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseThriftSavingsPlan();
    }
  },
  GIFT_OF_CASH("Gift of Cash") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseGiftOfCash();
    }
  },
  CASH_ON_HAND("Cash on Hand") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseCashOnHand();
    }
  },
  GRANT("Grant") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseGrant();
    }
  },
  BRIDGE_LOAN_NOT_DEPOSITED("Bridge Loan Not Deposited") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseBridgeLoanNotDeposited();
    }
  },
  INDIVIDUAL_DEVELOPMENT_ACCOUNT("Individual Development Account") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseIndividualDevelopmentAccount();
    }
  },
  LIFE_INSURANCE("Life Insurance") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseLifeInsurance();
    }
  },
  TRUST_ACCOUNT("Trust") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseTrustAccount();
    }
  },
  NET_SALE_PROCEEDS_FROM_REAL_ESTATE("Net Sale Proceeds from Real Estate") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseNetSaleProceedsFromRealEstate();
    }
  },
  NET_SALE_PROCEEDS_FROM_PERSONAL_PROPERTY("Net Sale Proceeds from Personal Property") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseNetSaleProceedsFromPersonalProperty();
    }
  },
  SECURED_LOAN("Secured Loan") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseSecuredLoan();
    }
  },
  UNSECURED_LOAN("Unsecured Loan") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseUnsecuredLoan();
    }
  },
  OTHER("Other") {
    @Override
    public <T> T visit(AssetTypeVisitor<T> visitor) {
      return visitor.caseOther();
    }
  };

  private final String description;

  TestAssetType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public abstract <T> T visit(AssetTypeVisitor<T> visitor);

  public interface AssetTypeVisitor<T> {
    T caseCheckingAccount();

    T caseSavingsAccount();

    T caseTaxableBrokerageAccount();

    T caseStock();

    T caseBond();

    T caseCertificateOfDeposit();

    T caseMoneyMarketFund();

    T caseMutualFund();

    T caseStockOptions();

    T caseCryptocurrency();

    T caseIRA();

    T caseFourZeroOneK();

    T caseThriftSavingsPlan();

    T caseGiftOfCash();

    T caseCashOnHand();

    T caseGrant();

    T caseBridgeLoanNotDeposited();

    T caseIndividualDevelopmentAccount();

    T caseLifeInsurance();

    T caseTrustAccount();

    T caseNetSaleProceedsFromRealEstate();

    T caseNetSaleProceedsFromPersonalProperty();

    T caseSecuredLoan();

    T caseUnsecuredLoan();

    T caseOther();

  }
}
