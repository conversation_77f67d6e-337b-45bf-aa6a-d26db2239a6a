package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestLiabilityType {
  MORTGAGE("Mortgage") {
    @Override
    public <T> T visit(LiabilityTypeVisitor<T> visitor) {
      return visitor.caseMortgage();
    }
  },

  HELOC("HELOC") {
    @Override
    public <T> T visit(LiabilityTypeVisitor<T> visitor) {
      return visitor.caseHeloc();
    }
  },

  HOME_EQUITY_LOAN("Home Equity Loan") {
    @Override
    public <T> T visit(LiabilityTypeVisitor<T> visitor) {
      return visitor.caseHomeEquityLoan();
    }
  };

  private final String description;

  TestLiabilityType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return this.description;
  }

  public abstract <T> T visit(LiabilityTypeVisitor<T> visitor);

  public interface LiabilityTypeVisitor<T> {

    T caseMortgage();

    T caseHeloc();

    T caseHomeEquityLoan();

  }

}
