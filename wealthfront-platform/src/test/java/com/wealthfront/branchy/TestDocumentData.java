package com.wealthfront.branchy;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.google.common.base.Preconditions;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
class TestDocumentData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private List<TestFileData> fileData;

  @Value(optional = true)
  private List<TestAssociatedEntity> associatedEntities;

  @Value(optional = true)
  private String vestaDocumentTypeString;

  @Value(optional = true)
  @TestNoopVestaSync
  private TestVestaDocumentType vestaDocumentType;

  @Value(optional = true)
  private Status status;

  @Value(optional = true)
  private ArchiveReason archiveReason;

  @Value(optional = true)
  private TestDocumentWorkflowData documentWorkflowData;

  TestDocumentData() { /* JSON */ }

  TestDocumentData(
      String id, TestEntityAction action, List<TestFileData> fileData, List<TestAssociatedEntity> associatedEntities,
      String vestaDocumentTypeString,
      TestVestaDocumentType vestaDocumentType, Status status, ArchiveReason archiveReason,
      TestDocumentWorkflowData documentWorkflowData) {
    this.id = id;
    this.action = action;
    this.fileData = fileData;
    this.associatedEntities = associatedEntities;
    this.vestaDocumentTypeString = vestaDocumentTypeString;
    this.vestaDocumentType = vestaDocumentType;
    this.status = status;
    this.archiveReason = archiveReason;
    this.documentWorkflowData = documentWorkflowData;
    validate();
  }

  void validate() {
    boolean allUploadedFiles = fileData == null ? false : fileData.stream()
        .map(TestFileData::getState)
        .map(Option::getOrThrow)
        .allMatch(state -> state == TestFileData.State.UPLOADED);
    Preconditions.checkArgument(archiveReason == null || status == Status.ARCHIVED,
        "Archive reason should only be defined for ARCHIVED status.");
    Preconditions.checkArgument(status != Status.ARCHIVED || archiveReason != null,
        "Archive reason must be defined for ARCHIVED status.");
    Preconditions.checkArgument(status != Status.PENDING_VESTA_SYNC || allUploadedFiles,
        "Attempting to set status to PENDING_VESTA_SYNC, all TestFileData's MUST be set to UPLOADED."
    );
  }

  public Option<List<TestFileData>> getFileData() {
    return Option.of(fileData);
  }

  public Option<TestFileData> getSingularFileData() {
    return getFileData().transform(fileData -> fileData.size() == 1 ? fileData.get(0) : null);
  }

  public Option<List<TestAssociatedEntity>> getAssociatedEntities() {
    return Option.of(associatedEntities);
  }

  public Option<String> getVestaDocumentTypeString() {
    return Option.of(vestaDocumentTypeString);
  }

  public Option<TestVestaDocumentType> getVestaDocumentType() {
    return Option.of(vestaDocumentType);
  }

  public Option<Status> getStatus() {
    return Option.of(status);
  }

  public Option<ArchiveReason> getArchiveReason() {
    return Option.of(archiveReason);
  }

  public Option<TestDocumentWorkflowData> getDocumentWorkflowData() {
    return Option.of(documentWorkflowData);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.DOCUMENT;
  }


  @Override
  public TestVestaOperationType getVestaOperationType() {
    if (status == null) {
      return TestVestaOperationType.NO_OP;
    }

    return switch (status) {
      case CREATED, FAILED_TO_S3, UPLOADED -> TestVestaOperationType.NO_OP;
      case PENDING_VESTA_SYNC -> TestVestaOperationType.DOCUMENT_UPLOAD;
      case REVIEWED, ACCEPTED, ARCHIVED -> TestVestaOperationType.DOCUMENT_UPDATE_STATUS;
      case EXPIRED -> TestVestaOperationType.NO_OP;
    };
  }

  @ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
  public enum Status {
    CREATED,
    FAILED_TO_S3,
    PENDING_VESTA_SYNC,
    UPLOADED,
    REVIEWED,
    ACCEPTED,
    ARCHIVED,
    EXPIRED;
  }

  @ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
  public enum ArchiveReason {
    ILLEGIBLE,
    INCOMPLETE,
    WRONG_DOCUMENT,
    NOT_A_MORTGAGE_DOCUMENT,
    REPLACED_WITH_NEW_VERSION,
    DUPLICATE,
    ASSOCIATED_TO_DIFFERENT_ASPECT,
    OTHER,
    MERGED_INTO_NEW_DOCUMENT,
    SPLIT_INTO_NEW_DOCUMENTS,
    ENTITY_DELETED,
    DOCUMENT_EXPIRED;
  }

}
